defmodule Repobot.SourceFiles.FormComponent.Save do
  @moduledoc """
  Command for saving source files from the FormComponent.
  Handles both creating new source files and updating existing ones.
  """

  use Repobot.Operation, type: :form

  import Ecto.Query
  import Ecto.Changeset

  alias Repobot.{Tags, Repo}
  alias Repobot.SourceFile

  require SourceFile

  schema(SourceFile,
    accept: [:user_id, :organization_id, :name, :content, :target_path]
  ) do
    %{
      optional(:is_template) => string(),
      optional(:tags) => any()
    }
  end

  @impl true
  def execute(%{changeset: changeset, action: :new}) do
    create_source_file(changeset)
  end

  def execute(%{changeset: changeset, action: :edit, source_file: source_file}) do
    update_source_file(changeset, source_file)
  end

  @impl true
  def execute(_previous_result, context) do
    execute(context)
  end

  @impl true
  def prepare(%{params: params, current_user: current_user} = context) do
    # Process tags from string to list and get tag entities
    tag_names = process_tags_input(Map.get(params, :tags, ""))
    tags = Tags.get_or_create_tags(tag_names, current_user)

    # Update params to remove tags string and add processed tags to context
    updated_params = Map.drop(params, [:tags, "tags"])

    {:ok,
     context
     |> Map.put(:params, updated_params)
     |> Map.put(:tags, tags)}
  end

  def cast_changeset(%{params: params, tags: tags, action: action} = context) do
    # Convert string boolean values to actual booleans
    normalized_params = normalize_boolean_params(params)

    changeset =
      case action do
        :new ->
          %SourceFile{}
          |> SourceFile.changeset(normalized_params)
          |> put_assoc(:tags, tags)

        :edit ->
          source_file = Map.get(context, :source_file)

          source_file
          |> SourceFile.changeset(normalized_params)
          |> put_assoc(:tags, tags)
      end

    {:ok, Map.put(context, :changeset, changeset)}
  end

  # Private functions

  defp create_source_file(changeset) do
    content = changeset.changes[:content]

    # Use a transaction to ensure both SourceFile and FileContent are created together
    Repo.transaction(fn ->
      # Create the SourceFile first (changeset already has tags associated via cast_changeset)
      case persist(changeset) do
        {:ok, source_file} ->
          # Create FileContent if content is provided
          if content do
            case Repo.insert(%Repobot.FileContent{
                   blob: content,
                   source_file_id: source_file.id
                 }) do
              {:ok, _fc} -> :ok
              {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
            end
          end

          # Reload with content projection
          from(sf in SourceFile,
            left_join: fc in assoc(sf, :file_content),
            where: sf.id == ^source_file.id,
            select: %{sf | content: fc.blob}
          )
          |> Repo.one!()
          |> Repo.preload([:tags, :repositories])

        {:error, changeset} ->
          Repo.rollback({:source_file_error, changeset})
      end
    end)
    |> case do
      {:ok, source_file} -> {:ok, source_file}
      {:error, {_error_type, changeset}} -> {:error, changeset}
      {:error, reason} -> {:error, reason}
    end
  end

  defp update_source_file(changeset, source_file) do
    source_file = Repo.preload(source_file, [:user, :tags, :source_repository, :file_content])
    content = changeset.changes[:content]

    # Prevent updates to read-only source files unless we're only changing the read_only flag itself
    if source_file.read_only do
      changed_fields = changeset.changes |> Map.keys() |> Enum.map(&to_string/1)
      non_readonly_changes = changed_fields -- ["read_only"]

      if not Enum.empty?(non_readonly_changes) do
        {:error,
         "Cannot update read-only source file. This file is managed by GitHub events from its template repository."}
      else
        # Only read_only field is being changed, allow the update
        case persist(changeset) do
          {:ok, updated_source_file} ->
            # Reload with content projection
            reloaded_source_file =
              from(sf in SourceFile,
                left_join: fc in assoc(sf, :file_content),
                where: sf.id == ^updated_source_file.id,
                select: %{sf | content: fc.blob}
              )
              |> Repo.one!()
              |> Repo.preload(:tags)

            {:ok, reloaded_source_file}

          error ->
            error
        end
      end
    else
      # Use a transaction to ensure both SourceFile and FileContent are updated together
      Repo.transaction(fn ->
        # Update FileContent if content is provided
        if content do
          case source_file.file_content do
            nil ->
              # Create new FileContent
              case Repo.insert(%Repobot.FileContent{
                     blob: content,
                     source_file_id: source_file.id
                   }) do
                {:ok, _fc} -> :ok
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end

            file_content ->
              # Update existing FileContent
              case Repo.update(Ecto.Changeset.change(file_content, blob: content)) do
                {:ok, _fc} -> :ok
                {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
              end
          end
        end

        # Update the SourceFile (changeset already has tags associated via cast_changeset)
        case persist(changeset) do
          {:ok, updated_source_file} ->
              end
            else
              updated_source_file
            end

          {:error, changeset} ->
            Repo.rollback({:source_file_error, changeset})
        end
      end)
      |> case do
        {:ok, updated_source_file} ->
          # Reload with content projection and preserve associations
          reloaded_source_file =
            from(sf in SourceFile,
              left_join: fc in assoc(sf, :file_content),
              where: sf.id == ^updated_source_file.id,
              select: %{sf | content: fc.blob}
            )
            |> Repo.one!()
            |> Repo.preload([:tags, :repositories])

          {:ok, reloaded_source_file}

        {:error, {_error_type, changeset}} ->
          {:error, changeset}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  # Helper functions

  defp process_tags_input(tags) when is_binary(tags) do
    tags
    |> String.split(",")
    |> Enum.map(&String.trim/1)
    |> Enum.reject(&(&1 == ""))
  end

  defp process_tags_input(tags) when is_list(tags), do: tags
  defp process_tags_input(_), do: []
end
