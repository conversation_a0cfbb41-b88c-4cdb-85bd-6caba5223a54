defmodule Repobot do
  @moduledoc """
  Repobot keeps the contexts that define your domain
  and business logic.

  Contexts are also responsible for managing your data, regardless
  if it comes from the database, an external API or others.
  """
  require Drops.Operations.Extension
  require Drops.Operations.Extensions.Ecto

  alias Drops.Operations

  Operations.Extension.register_extension(Operations.Extensions.Ecto)

  defmodule Operation do
    use Drops.Operations, repo: Repobot.Repo
  end
end
