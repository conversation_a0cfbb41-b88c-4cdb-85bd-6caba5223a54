defmodule RepobotWeb.Live.SourceFiles.FormComponent do
  use RepobotWeb, :live_component

  alias <PERSON>obot.SourceFiles
  alias Repobot.SourceFiles.FormComponent.Save
  alias Repobot.{Repo, TemplateContext}

  # Calculate rows based on content length, with min and max bounds
  defp calculate_rows(content) when is_binary(content) do
    line_count = content |> String.split("\n") |> length()
    min(max(line_count, 10), 30)
  end

  defp calculate_rows(_), do: 10

  def render(assigns) do
    ~H"""
    <div>
      <%= if @source_file.read_only do %>
        <div class="px-6 py-4 bg-amber-50 border-b border-amber-200">
          <div class="flex">
            <div class="flex-shrink-0">
              <.icon name="hero-lock-closed" class="h-5 w-5 text-amber-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-amber-800">
                Read-Only Source File
              </h3>
              <div class="mt-2 text-sm text-amber-700">
                <p>
                  This source file is read-only because it originates from a template repository.
                  Changes are managed automatically through GitHub events and webhook handlers.
                  To modify this file, edit it directly in the source repository:
                  <%= if @source_file.source_repository do %>
                    <.link
                      navigate={~p"/repositories/#{@source_file.source_repository}"}
                      class="font-medium underline hover:text-amber-900"
                    >
                      {@source_file.source_repository.full_name}
                    </.link>
                  <% end %>
                </p>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <.form
        for={@form}
        id="source-file-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="px-6 py-5 space-y-6">
          <div>
            <.input
              field={@form[:name]}
              type="text"
              label="Name"
              id="source-file-name"
              phx-change="name_changed"
              phx-target={@myself}
              disabled={@source_file.read_only}
            />
          </div>

          <div>
            <.input
              field={@form[:target_path]}
              type="text"
              label="Target Path"
              id="source-file-target-path"
              disabled={@source_file.read_only}
            />
            <p class="mt-1 text-sm text-slate-500">
              The path where this file will be created in repositories. Defaults to the file name.
            </p>
          </div>

          <div>
            <fieldset class="fieldset">
              <label class="label">Tags</label>
              <input
                type="text"
                name="source_file[tags]"
                value={@tag_string}
                class="input"
                placeholder="Enter tags separated by commas"
                phx-target={@myself}
                phx-change="update_tags"
                disabled={@source_file.read_only}
              />
              <p class="label">
                Enter tags separated by commas (e.g. "config, documentation, ci")
              </p>
            </fieldset>
          </div>

          <div>
            <div class="flex items-center gap-2">
              <div class="flex items-center gap-1">
                <fieldset class="fieldset">
                  <label class="label">
                    <input
                      type="checkbox"
                      id="source-file-is-template"
                      name="source_file[is_template]"
                      value="true"
                      checked={@form[:is_template].value}
                      class="checkbox checkbox-sm"
                      phx-click="toggle_template_checkbox"
                      phx-target={@myself}
                      disabled={@source_file.read_only}
                    /> Use as template
                  </label>
                </fieldset>
                <.btn
                  phx-click="toggle_template_modal"
                  phx-target={@myself}
                  variant="ghost"
                  size="sm"
                  type="button"
                  circle
                >
                  <.icon name="hero-question-mark-circle" class="h-4 w-4" />
                </.btn>
              </div>
            </div>
            <p class="mt-1 text-sm text-slate-500">
              Enable to use Liquid template variables in the file content.
            </p>

            <%= if @show_template_conversion_warning do %>
              <div class="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-md">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <.icon name="hero-exclamation-triangle" class="h-5 w-5 text-amber-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-amber-800">
                      Template Conversion Warning
                    </h3>
                    <div class="mt-2 text-sm text-amber-700">
                      <p>
                        Converting this file to a template will:
                      </p>
                      <ul class="mt-1 list-disc list-inside">
                        <li>
                          Rename the file from
                          <code class="bg-amber-100 px-1 rounded">
                            {get_original_filename(@form[:name].value)}
                          </code>
                          to
                          <code class="bg-amber-100 px-1 rounded">
                            {get_template_filename(@form[:name].value)}
                          </code>
                        </li>
                        <li>Update the file in the source GitHub repository</li>
                        <li>
                          Set the target path to
                          <code class="bg-amber-100 px-1 rounded">
                            {get_original_filename(@form[:name].value)}
                          </code>
                        </li>
                      </ul>
                      <p class="mt-2 font-medium">
                        This action will modify files in your GitHub repository and cannot be easily undone.
                      </p>
                    </div>
                    <div class="mt-4 flex gap-2">
                      <.btn
                        phx-click="confirm_template_conversion"
                        phx-target={@myself}
                        variant="error"
                        size="sm"
                      >
                        Proceed with Conversion
                      </.btn>
                      <.btn
                        phx-click="cancel_template_conversion"
                        phx-target={@myself}
                        variant="outline"
                        size="sm"
                      >
                        Cancel
                      </.btn>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>

          <div>
            <fieldset class="fieldset">
              <label class="label">Content</label>
              <textarea
                id={@form[:content].id}
                name={@form[:content].name}
                class="textarea font-mono"
                rows={calculate_rows(@form[:content].value)}
                disabled={@source_file.read_only}
              ><%= @form[:content].value %></textarea>
              <.error :for={msg <- @form[:content].errors}>
                {translate_error(msg)}
              </.error>
            </fieldset>
          </div>
        </div>

        <div class="px-6 py-4 bg-slate-50 border-t border-slate-200 flex justify-end">
          <.button type="submit" variant="primary" disabled={@source_file.read_only}>
            Save
          </.button>
        </div>
      </.form>

      <%= if @show_template_modal do %>
        <div class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
              <h3 class="text-lg font-medium text-slate-900">
                Available Template Variables
              </h3>
              <.btn
                phx-click="toggle_template_modal"
                phx-target={@myself}
                variant="ghost"
                size="sm"
                circle
              >
                <.icon name="hero-x-mark" class="w-5 h-5" />
              </.btn>
            </div>
            <div class="p-6">
              <p class="text-sm text-slate-600 mb-4">
                Use these variables in your template with
                <code class="bg-slate-100 px-1 rounded">
                  &lbrace;&lbrace; variable &rbrace;&rbrace;
                </code>
              </p>
              <div class="grid grid-cols-1 gap-4">
                <%= for {var, desc} <- TemplateContext.available_variables() do %>
                  <div class="flex items-start">
                    <code class="text-sm bg-slate-100 px-2 py-1 rounded">
                      &lbrace;&lbrace; {var} &rbrace;&rbrace;
                    </code>
                    <span class="text-sm text-slate-600 ml-3 mt-1">{desc}</span>
                  </div>
                <% end %>
              </div>
              <div class="mt-6">
                <h4 class="text-sm font-medium text-slate-900 mb-2">Example Template</h4>
                <pre class="bg-slate-100 p-4 rounded text-sm overflow-x-auto"><code>MIT License

    Copyright (c) &lbrace;&lbrace; created_at | date: "%Y" &rbrace;&rbrace; &lbrace;&lbrace; owner &rbrace;&rbrace;

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software...</code></pre>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  def update(%{source_file: source_file} = assigns, socket) do
    changeset = SourceFiles.change_source_file(source_file)
    source_file = Repo.preload(source_file, :tags)
    tag_string = Enum.map_join(source_file.tags, ", ", & &1.name)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:source_file, source_file)
     |> assign(:tag_string, tag_string)
     |> assign(:show_template_vars, source_file.is_template)
     |> assign(:show_template_modal, false)
     |> assign(:show_template_conversion_warning, false)
     |> assign_form(changeset)}
  end

  def handle_event("validate", %{"source_file" => source_file_params}, socket) do
    # Update tag string in socket assigns
    tag_string = Map.get(source_file_params, "tags", "")

    # Log the incoming parameters for debugging
    require Logger
    Logger.debug("Validate params: #{inspect(source_file_params)}")

    # If name is set but target_path is not, set target_path to name
    source_file_params =
      if source_file_params["name"] && source_file_params["name"] != "" &&
           (!source_file_params["target_path"] || source_file_params["target_path"] == "") do
        # Set target_path to the full name
        updated_params = Map.put(source_file_params, "target_path", source_file_params["name"])
        Logger.debug("Updated target_path: #{inspect(updated_params)}")
        updated_params
      else
        source_file_params
      end

    changeset =
      socket.assigns.source_file
      |> SourceFiles.change_source_file(source_file_params)
      |> Map.put(:action, :validate)

    # Update show_template_vars based on the is_template value in the params
    show_template_vars = Map.get(source_file_params, "is_template", "false") == "true"

    {:noreply,
     socket
     |> assign(:tag_string, tag_string)
     |> assign(:show_template_vars, show_template_vars)
     |> assign_form(changeset)}
  end

  def handle_event("toggle_template_checkbox", _params, socket) do
    # Get the current form data
    current_form = socket.assigns.form
    current_is_template = current_form[:is_template].value || false
    source_file = socket.assigns.source_file

    # Check if we're converting from non-template to template and have a source repository
    if not current_is_template and not is_nil(source_file.source_repository_id) do
      # Show warning for template conversion
      {:noreply, assign(socket, :show_template_conversion_warning, true)}
    else
      # Safe toggle - either converting from template to non-template or no source repository
      {:noreply, toggle_template_value(socket)}
    end
  end

  def handle_event("confirm_template_conversion", _params, socket) do
    # User confirmed the conversion, proceed with immediate save
    require Logger

    # Get current form values and toggle is_template
    current_form = socket.assigns.form

    # For template conversion, we need to ensure the target_path is set correctly
    # The target_path should be the original filename without .liquid extension
    original_filename = get_original_filename(current_form[:name].value)

    current_values = %{
      "name" => current_form[:name].value,
      "target_path" => original_filename,
      "content" => current_form[:content].value,
      "is_template" => true,
      "tags" => socket.assigns.tag_string
    }

    Logger.debug("Confirming template conversion with values: #{inspect(current_values)}")

    # Process tags and save immediately
    source_file_params = process_tags_param(current_values)

    case SourceFiles.update_source_file(socket.assigns.source_file, source_file_params) do
      {:ok, source_file} ->
        send(self(), {:source_file_updated, source_file})

        {:noreply,
         socket
         |> assign(:show_template_conversion_warning, false)
         |> assign(:source_file, source_file)
         |> assign_form(SourceFiles.change_source_file(source_file))
         |> put_flash(:info, "Source file converted to template successfully")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply,
         socket
         |> assign(:show_template_conversion_warning, false)
         |> assign_form(changeset)}

      {:error, error_message} when is_binary(error_message) ->
        # Handle GitHub operation errors
        {:noreply,
         socket
         |> assign(:show_template_conversion_warning, false)
         |> put_flash(:error, error_message)}
    end
  end

  def handle_event("cancel_template_conversion", _params, socket) do
    # User cancelled the conversion, hide warning
    {:noreply, assign(socket, :show_template_conversion_warning, false)}
  end

  def handle_event("update_tags", %{"source_file" => %{"tags" => tags}}, socket) do
    # Just update the tag_string in the socket assigns without revalidating the form
    {:noreply, assign(socket, :tag_string, tags)}
  end

  def handle_event("toggle_template_modal", _params, socket) do
    {:noreply, assign(socket, :show_template_modal, !socket.assigns.show_template_modal)}
  end

  def handle_event("name_changed", %{"source_file" => %{"name" => name}}, socket) do
    # When name changes, update the target path if it's empty
    require Logger
    Logger.debug("Name changed to: #{name}")

    # Get the current form data
    current_form = socket.assigns.form
    current_target_path = current_form[:target_path].value

    # Only update target_path if it's empty or not set
    if current_target_path == nil || current_target_path == "" do
      # Create a new changeset with the updated target_path
      changeset =
        socket.assigns.source_file
        |> SourceFiles.change_source_file(%{"name" => name, "target_path" => name})
        |> Map.put(:action, :validate)

      Logger.debug("Updated target_path to match name: #{name}")

      {:noreply, assign_form(socket, changeset)}
    else
      # If target_path already has a value, just validate the name change
      changeset =
        socket.assigns.source_file
        |> SourceFiles.change_source_file(%{"name" => name})
        |> Map.put(:action, :validate)

      {:noreply, assign_form(socket, changeset)}
    end
  end

  def handle_event("save", %{"source_file" => source_file_params}, socket) do
    # Ensure we use the current tag_string from the socket assigns
    source_file_params = Map.put(source_file_params, "tags", socket.assigns.tag_string)

    # Log the save parameters for debugging
    require Logger
    Logger.debug("Save params: #{inspect(source_file_params)}")

    # Prepare parameters for the Save command
    params =
      source_file_params
      |> Map.put("user_id", socket.assigns.current_user.id)
      |> Map.put("organization_id", socket.assigns.current_organization.id)

    # Prepare context for the Save command
    context = %{
      params: params,
      action: socket.assigns.action,
      current_user: socket.assigns.current_user,
      current_organization: socket.assigns.current_organization
    }

    # Add source_file to context for edit operations
    context =
      case socket.assigns.action do
        :edit -> Map.put(context, :source_file, socket.assigns.source_file)
        :new -> context
      end

    # Call Save command with context
    result = Save.call(context)

    case result do
      {:ok, source_file} ->
        case socket.assigns.action do
          :edit ->
            send(self(), {:source_file_updated, source_file})
            {:noreply, socket}

          :new ->
            {:noreply,
             socket
             |> put_flash(:info, "Source file created successfully")
             |> push_navigate(to: ~p"/source-files")}
        end

      {:error, changeset} when is_struct(changeset, Ecto.Changeset) ->
        {:noreply, assign_form(socket, changeset)}

      {:error, error_message} when is_binary(error_message) ->
        # Handle GitHub operation errors
        {:noreply,
         socket
         |> put_flash(:error, error_message)
         |> assign(:show_template_conversion_warning, false)}
    end
  end

  defp toggle_template_value(socket) do
    # Get the current form data
    current_form = socket.assigns.form
    current_is_template = current_form[:is_template].value || false

    # Create a new changeset with the toggled is_template value
    # but preserve all other form values
    current_values = %{
      "name" => current_form[:name].value,
      "target_path" => current_form[:target_path].value,
      "content" => current_form[:content].value,
      "is_template" => !current_is_template
    }

    # Log the current values for debugging
    require Logger
    Logger.debug("Current form values: #{inspect(current_values)}")

    changeset =
      socket.assigns.source_file
      |> SourceFiles.change_source_file(current_values)
      |> Map.put(:action, :validate)

    # Toggle the template vars visibility and return just the socket
    socket
    |> assign(:show_template_vars, !current_is_template)
    |> assign_form(changeset)
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  defp process_tags_param(%{"tags" => tags} = params) when is_binary(tags) do
    tag_names =
      tags
      |> String.split(",")
      |> Enum.map(&String.trim/1)
      |> Enum.reject(&(&1 == ""))

    Map.put(params, "tags", tag_names)
  end

  defp process_tags_param(params), do: params

  # Helper functions for template conversion warning
  defp get_original_filename(filename) when is_binary(filename) do
    # Remove .liquid extension if present to get the original filename
    if String.ends_with?(filename, ".liquid") do
      String.slice(filename, 0..-8//1)
    else
      filename
    end
  end

  defp get_original_filename(_), do: ""

  defp get_template_filename(filename) when is_binary(filename) do
    # Add .liquid extension if not already present
    original = get_original_filename(filename)
    original <> ".liquid"
  end

  defp get_template_filename(_), do: ".liquid"
end
